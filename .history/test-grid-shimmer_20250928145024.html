<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid Shimmer Test</title>
    <link rel="stylesheet" href="components/data-grid/snap-grid.css">
</head>
<body>
    <div id="grid-container" style="width: 100%; height: 500px; margin: 20px;"></div>
    
    <button onclick="testShimmer()" style="margin: 20px; padding: 10px 20px;">Test Shimmer Effect</button>
    <button onclick="debugShimmer()" style="margin: 20px; padding: 10px 20px;">Debug Shimmer</button>
    
    <script src="components/data-grid/snap-grid.js"></script>
    <script>
        let grid;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize grid
            const container = document.getElementById('grid-container');
            grid = new SnapGrid(container, {
                columns: [
                    { field: 'id', headerName: 'ID', width: 100 },
                    { field: 'name', headerName: 'Name', width: 200 },
                    { field: 'value', headerName: 'Value', width: 150 }
                ]
            });
        });
        
        function testShimmer() {
            console.log('Testing shimmer effect...');
            if (grid) {
                // Trigger loading state
                grid.showLoadingState(true);

                // Hide after 5 seconds for testing
                setTimeout(() => {
                    grid.showLoadingState(false);
                }, 5000);
            }
        }

        function debugShimmer() {
            console.log('Debugging shimmer...');
            if (grid && grid.loadingOverlay) {
                const textElement = grid.loadingOverlay.querySelector('.snap-grid-loading-text');
                console.log('Text element found:', textElement);
                console.log('Text element classes:', textElement?.className);
                console.log('Text element content:', textElement?.textContent);
                console.log('Text element innerHTML:', textElement?.innerHTML);

                // Try to manually initialize shimmer
                if (textElement && !textElement.querySelector('.shimmer-char')) {
                    console.log('Manually initializing shimmer...');
                    const text = textElement.textContent;
                    textElement.innerHTML = '';
                    textElement.classList.add('shimmer-text');

                    for (let i = 0; i < text.length; i++) {
                        const char = text[i];
                        const span = document.createElement('span');
                        span.className = 'shimmer-char';
                        span.textContent = char === ' ' ? '\u00A0' : char;
                        span.style.animationDelay = `${i * 0.1}s`;
                        textElement.appendChild(span);
                    }
                    console.log('Manual shimmer initialization complete');
                }
            } else {
                console.log('No grid or loading overlay found');
            }
        }
    </script>
</body>
</html>
