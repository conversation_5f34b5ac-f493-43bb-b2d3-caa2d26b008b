<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid Shimmer Test</title>
    <link rel="stylesheet" href="components/data-grid/snap-grid.css">
</head>
<body>
    <div id="grid-container" style="width: 100%; height: 500px; margin: 20px;"></div>
    
    <button onclick="testShimmer()" style="margin: 20px; padding: 10px 20px;">Test Shimmer Effect</button>
    
    <script src="components/data-grid/snap-grid.js"></script>
    <script>
        let grid;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize grid
            const container = document.getElementById('grid-container');
            grid = new SnapGrid(container, {
                columns: [
                    { field: 'id', headerName: 'ID', width: 100 },
                    { field: 'name', headerName: 'Name', width: 200 },
                    { field: 'value', headerName: 'Value', width: 150 }
                ]
            });
        });
        
        function testShimmer() {
            console.log('Testing shimmer effect...');
            if (grid) {
                // Trigger loading state
                grid.showLoadingState(true);
                
                // Hide after 5 seconds for testing
                setTimeout(() => {
                    grid.showLoadingState(false);
                }, 5000);
            }
        }
    </script>
</body>
</html>
